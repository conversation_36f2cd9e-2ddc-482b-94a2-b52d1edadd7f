/* Layout Fix - T<PERSON><PERSON> ưu bố cục sidebar và main content */

/* Tối ưu sidebar width để có thêm không gian cho main content */
.sidebar {
    width: 220px !important; /* <PERSON><PERSON><PERSON><PERSON> từ 240px xuống 220px */
}

/* Điều chỉnh main content margin để phù hợp với sidebar mới */
.main-content {
    margin-left: 220px !important; /* Gi<PERSON>m từ 240px xuống 220px */
    padding: 0 !important; /* Loại bỏ padding không cần thiết */
}

/* Tối ưu container để sử dụng toàn bộ không gian có sẵn */
.container-fluid {
    max-width: 100% !important; /* Loại bỏ giới hạn 1400px */
    padding-left: 8px !important; /* Giảm padding để có thêm không gian */
    padding-right: 8px !important;
}

/* Tối ưu main-header padding */
.main-header {
    padding: 0.75rem 1rem !important; /* <PERSON><PERSON><PERSON><PERSON> padding từ 1rem 1.5rem */
}

/* T<PERSON>i ưu card padding */
.card {
    margin-bottom: 15px !important; /* <PERSON><PERSON><PERSON><PERSON> từ 20px */
}

.card-header {
    padding: 0.75rem 1rem !important; /* Giảm padding */
}

.card-body {
    padding: 1rem !important; /* Giảm padding */
}

/* Container chính cho bảng */
.table-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow: visible !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Bảng khách hàng - tối ưu để fit trong không gian mới */
.customers-table {
    width: 100% !important;
    max-width: 100% !important;
    table-layout: fixed !important;
    margin: 0 !important;
}

/* Cập nhật width cột để phù hợp với không gian mới */
.customers-table th:nth-child(1), /* Mã KH */
.customers-table td:nth-child(1) {
    width: 7% !important;
    min-width: 70px !important;
    max-width: 90px !important;
}

.customers-table th:nth-child(2), /* Khách hàng */
.customers-table td:nth-child(2) {
    width: 22% !important;
    min-width: 200px !important;
    max-width: 280px !important;
}

.customers-table th:nth-child(3), /* Liên hệ */
.customers-table td:nth-child(3) {
    width: 20% !important;
    min-width: 180px !important;
    max-width: 250px !important;
}

.customers-table th:nth-child(4), /* Dịch vụ */
.customers-table td:nth-child(4) {
    width: 14% !important;
    min-width: 120px !important;
    max-width: 160px !important;
}

.customers-table th:nth-child(5), /* Ngày tạo */
.customers-table td:nth-child(5) {
    width: 11% !important;
    min-width: 100px !important;
    max-width: 130px !important;
}

.customers-table th:nth-child(6), /* Thao tác - QUAN TRỌNG */
.customers-table td:nth-child(6) {
    width: 26% !important;
    min-width: 240px !important; /* Tăng từ 220px lên 240px */
    max-width: 320px !important; /* Tăng từ 300px lên 320px */
    text-align: center !important;
    white-space: nowrap !important;
    overflow: visible !important;
    position: relative !important;
    padding: 8px 12px !important; /* Tăng padding ngang */
}

/* Đảm bảo button group có đủ không gian */
.customers-table .btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 3px !important; /* Tăng gap từ 2px lên 3px */
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    min-width: 200px !important; /* Đảm bảo min-width cho button group */
}

/* Tối ưu các nút hành động */
.customers-table .btn-group .btn {
    padding: 4px 8px !important; /* Tăng padding từ 3px 6px */
    font-size: 0.7rem !important; /* Tăng từ 0.65rem */
    line-height: 1.3 !important;
    min-width: 35px !important; /* Tăng từ 30px */
    max-width: 50px !important; /* Thêm max-width */
    border-radius: 4px !important;
    margin: 0 1px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Icon trong nút */
.customers-table .btn-group .btn i {
    font-size: 0.75rem !important; /* Tăng từ 0.7rem */
    width: auto !important;
    margin: 0 !important;
}

/* Đảm bảo nút xóa không bị ẩn */
.customers-table .btn-group .btn:last-child {
    position: relative !important;
    z-index: 10 !important;
}

/* Table responsive container */
.table-responsive {
    overflow-x: auto !important;
    overflow-y: visible !important;
    max-width: 100% !important;
    border-radius: 0.375rem;
    margin: 0 !important;
    padding: 0 !important;
}

/* Scrollbar styling */
.table-responsive::-webkit-scrollbar {
    height: 8px !important; /* Tăng từ 6px */
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f5f9 !important;
    border-radius: 4px !important;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #cbd5e1 !important;
    border-radius: 4px !important;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #94a3b8 !important;
}

/* Responsive breakpoints được cập nhật */
@media (max-width: 1600px) {
    .customers-table {
        font-size: 0.7rem;
    }
    
    .customers-table .btn-group .btn {
        padding: 3px 6px !important;
        font-size: 0.65rem !important;
        min-width: 32px !important;
    }
}

@media (max-width: 1400px) {
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        min-width: 220px !important;
    }
    
    .customers-table .btn-group .btn {
        padding: 3px 5px !important;
        font-size: 0.6rem !important;
        min-width: 30px !important;
    }
}

@media (max-width: 1200px) {
    /* Ẩn cột liên hệ */
    .customers-table th:nth-child(3),
    .customers-table td:nth-child(3) {
        display: none !important;
    }
    
    /* Điều chỉnh width các cột còn lại */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 9% !important;
    }
    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 38% !important;
    }
    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 18% !important;
    }
    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        width: 13% !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 22% !important;
        min-width: 180px !important;
    }
}

@media (max-width: 992px) {
    /* Sidebar collapse trên tablet */
    .sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
    }
    
    .sidebar.show {
        transform: translateX(0) !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

@media (max-width: 768px) {
    /* Ẩn cột ngày tạo */
    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        display: none !important;
    }
    
    /* Điều chỉnh width cho mobile */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 12% !important;
    }
    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 45% !important;
    }
    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 18% !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 25% !important;
        min-width: 140px !important;
    }
    
    .customers-table .btn-group .btn {
        padding: 2px 4px !important;
        font-size: 0.55rem !important;
        min-width: 26px !important;
    }
}

/* Fix cho dropdown menu */
.customers-table .dropdown-menu {
    position: absolute !important;
    z-index: 1055 !important;
    right: 0 !important;
    left: auto !important;
    min-width: 160px !important;
}

/* Đảm bảo tooltip hiển thị đúng */
.tooltip {
    z-index: 1060 !important;
}

/* Fix cho màn hình siêu rộng */
@media (min-width: 1920px) {
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 24% !important;
        min-width: 280px !important;
    }
    
    .customers-table .btn-group .btn {
        padding: 5px 10px !important;
        font-size: 0.75rem !important;
        min-width: 40px !important;
    }
}
