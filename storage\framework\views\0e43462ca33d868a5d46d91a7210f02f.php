<?php $__env->startSection('title', 'Dashboard'); ?>
<?php $__env->startSection('page-title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<!-- Thống kê tổng quan -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--primary-gradient);">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($totalCustomers)); ?></h2>
                <p class="stats-label mb-0">Khách hàng</p>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        Tăng trưởng tốt
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--success-gradient);">
                    <i class="fas fa-box"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($totalServicePackages)); ?></h2>
                <p class="stats-label mb-0">Gói dịch vụ</p>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="fas fa-chart-line me-1"></i>
                        Đa dạng sản phẩm
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--info-gradient);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($totalActiveServices)); ?></h2>
                <p class="stats-label mb-0">Dịch vụ hoạt động</p>
                <div class="mt-2">
                    <small class="text-primary">
                        <i class="fas fa-sync-alt me-1"></i>
                        Đang vận hành
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--warning-gradient);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($expiringSoonServices)); ?></h2>
                <p class="stats-label mb-0">Sắp hết hạn</p>
                <div class="mt-2">
                    <small class="text-warning">
                        <i class="fas fa-clock me-1"></i>
                        Cần chú ý
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lead Statistics -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-user-plus text-primary me-2"></i>
            Thống kê Lead (Khách hàng tiềm năng)
        </h4>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--info-gradient);">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($totalLeads)); ?></h2>
                <p class="stats-label mb-0">Tổng Lead</p>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="fas fa-chart-line me-1"></i>
                        Tất cả lead
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--success-gradient);">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($newLeads)); ?></h2>
                <p class="stats-label mb-0">Lead mới</p>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-plus me-1"></i>
                        Chưa xử lý
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--warning-gradient);">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($followUpTodayLeads)); ?></h2>
                <p class="stats-label mb-0">Cần theo dõi hôm nay</p>
                <div class="mt-2">
                    <small class="text-warning">
                        <i class="fas fa-clock me-1"></i>
                        Hôm nay
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--danger-gradient);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($overdueLeads)); ?></h2>
                <p class="stats-label mb-0">Quá hạn</p>
                <div class="mt-2">
                    <small class="text-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Cần xử lý gấp
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body">
                <div class="stats-icon mb-3" style="background: var(--primary-gradient);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="stats-number"><?php echo e(number_format($convertedThisMonth)); ?></h2>
                <p class="stats-label mb-0">Chuyển đổi tháng này</p>
                <div class="mt-2">
                    <small class="text-primary">
                        <i class="fas fa-trophy me-1"></i>
                        Thành công
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lead cần chú ý -->
<div class="row g-4 mb-4">
    <div class="col-xl-6 col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        Lead khẩn cấp
                    </h5>
                    <a href="<?php echo e(route('admin.leads.index', ['priority' => 'urgent'])); ?>"
                        class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($urgentLeads->count() > 0): ?>
                <?php $__currentLoopData = $urgentLeads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex align-items-center p-3 border-bottom">
                    <div class="avatar-sm me-3">
                        <div class="avatar-initial bg-danger rounded-circle">
                            <?php echo e(substr($lead->name, 0, 1)); ?>

                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?php echo e($lead->name); ?></h6>
                        <p class="text-muted mb-1">
                            <i class="fas fa-phone me-1"></i><?php echo e($lead->phone); ?>

                            <?php if($lead->servicePackage): ?>
                            | <?php echo e($lead->servicePackage->name); ?>

                            <?php endif; ?>
                        </p>
                        <small class="text-muted">
                            Tạo: <?php echo e($lead->created_at->diffForHumans()); ?>

                            <?php if($lead->assignedUser): ?>
                            | PV: <?php echo e($lead->assignedUser->name); ?>

                            <?php endif; ?>
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-danger"><?php echo e($lead->getPriorityName()); ?></span>
                        <br><small class="text-muted"><?php echo e($lead->getStatusName()); ?></small>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                <div class="text-center py-4">
                    <div class="empty-icon mb-3">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <h5 class="text-muted">Không có lead khẩn cấp</h5>
                    <p class="text-muted mb-0">Tất cả lead đều được xử lý tốt</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        Lead quá hạn theo dõi
                    </h5>
                    <a href="<?php echo e(route('admin.leads.index', ['overdue' => 1])); ?>"
                        class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($overdueLeadsList->count() > 0): ?>
                <?php $__currentLoopData = $overdueLeadsList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex align-items-center p-3 border-bottom">
                    <div class="avatar-sm me-3">
                        <div class="avatar-initial bg-warning rounded-circle">
                            <?php echo e(substr($lead->name, 0, 1)); ?>

                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?php echo e($lead->name); ?></h6>
                        <p class="text-muted mb-1">
                            <i class="fas fa-phone me-1"></i><?php echo e($lead->phone); ?>

                            <?php if($lead->servicePackage): ?>
                            | <?php echo e($lead->servicePackage->name); ?>

                            <?php endif; ?>
                        </p>
                        <small class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Quá hạn <?php echo e($lead->getDaysOverdue()); ?> ngày
                        </small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">
                            <?php echo e($lead->next_follow_up_at->format('d/m/Y')); ?>

                        </small>
                        <br><span class="badge <?php echo e($lead->getStatusBadgeClass()); ?>"><?php echo e($lead->getStatusName()); ?></span>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                <div class="text-center py-4">
                    <div class="empty-icon mb-3">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <h5 class="text-muted">Không có lead quá hạn</h5>
                    <p class="text-muted mb-0">Tất cả lead đều được theo dõi đúng hạn</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Dịch vụ sắp hết hạn -->
    <div class="col-xl-8 col-lg-7">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        Dịch vụ sắp hết hạn (5 ngày tới)
                    </h5>
                    <a href="<?php echo e(route('admin.customer-services.index', ['filter' => 'expiring'])); ?>"
                        class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($expiringSoon->count() > 0): ?>
                <div class="table-container">
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user me-2"></i>Khách hàng</th>
                                <th><i class="fas fa-box-open me-2"></i>Dịch vụ</th>
                                <th><i class="fas fa-calendar-times me-2"></i>Hết hạn</th>
                                <th><i class="fas fa-hourglass-half me-2"></i>Còn lại</th>
                                <th><i class="fas fa-cogs me-2"></i>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $expiringSoon; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial rounded-circle bg-primary text-white me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            <?php echo e(substr($service->customer->name, 0, 1)); ?>

                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo e($service->customer->name); ?></div>
                                            <small class="text-muted"><?php echo e($service->customer->customer_code); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold"><?php echo e($service->servicePackage->name); ?></div>
                                    <small class="text-muted"><?php echo e($service->servicePackage->category->name ?? 'N/A'); ?></small>
                                </td>
                                <td>
                                    <div class="fw-semibold"><?php echo e($service->expires_at->format('d/m/Y')); ?></div>
                                    <small class="text-muted"><?php echo e($service->expires_at->format('H:i')); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $daysRemaining = $service->getDaysRemaining();
                                    ?>
                                    <?php if($daysRemaining <= 1): ?>
                                        <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <?php echo e($daysRemaining); ?> ngày
                                        </span>
                                        <?php elseif($daysRemaining <= 3): ?>
                                            <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo e($daysRemaining); ?> ngày
                                            </span>
                                            <?php else: ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo e($daysRemaining); ?> ngày
                                            </span>
                                            <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo e(route('admin.customers.show', $service->customer)); ?>"
                                            class="btn btn-sm btn-outline-primary"
                                            data-bs-toggle="tooltip"
                                            title="Xem chi tiết khách hàng">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.customer-services.show', $service)); ?>"
                                            class="btn btn-sm btn-outline-info"
                                            data-bs-toggle="tooltip"
                                            title="Xem chi tiết dịch vụ">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-3x text-success opacity-50"></i>
                    </div>
                    <h5 class="text-muted">Không có dịch vụ nào sắp hết hạn</h5>
                    <p class="text-muted mb-0">Tất cả dịch vụ đều hoạt động bình thường</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Khách hàng mới nhất -->
    <div class="col-xl-4 col-lg-5">
        <div class="card ">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2 text-primary"></i>
                    Khách hàng mới nhất
                </h5>
            </div>
            <div class="card-body">
                <?php if($recentCustomers->count() > 0): ?>
                <div class="customer-list">
                    <?php $__currentLoopData = $recentCustomers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="customer-item d-flex align-items-center p-3 rounded-3 mb-3" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%); border-left: 4px solid var(--primary-gradient);">
                        <div class="avatar-initial rounded-circle me-3" style="width: 50px; height: 50px; background: var(--primary-gradient); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem;">
                            <?php echo e(substr($customer->name, 0, 1)); ?>

                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold mb-1"><?php echo e($customer->name); ?></div>
                            <div class="d-flex align-items-center text-muted small">
                                <i class="fas fa-id-card me-1"></i>
                                <span class="me-3"><?php echo e($customer->customer_code); ?></span>
                                <i class="fas fa-boxes me-1"></i>
                                <span><?php echo e($customer->customerServices->count()); ?> dịch vụ</span>
                            </div>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    <?php echo e($customer->created_at->diffForHumans()); ?>

                                </small>
                            </div>
                        </div>
                        <div class="ms-2">
                            <a href="<?php echo e(route('admin.customers.show', $customer)); ?>"
                                class="btn btn-sm btn-outline-primary rounded-pill"
                                data-bs-toggle="tooltip"
                                title="Xem chi tiết">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div class="text-center mt-3">
                    <a href="<?php echo e(route('admin.customers.index')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        Xem tất cả khách hàng
                    </a>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-user-plus fa-2x text-muted opacity-50"></i>
                    </div>
                    <p class="text-muted mb-0">Chưa có khách hàng mới</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<!-- Dịch vụ phổ biến và Bài đăng -->
<div class="row g-4 mt-2">
    <!-- Dịch vụ phổ biến -->
    <div class="col-12">
        <div class="card ">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2 text-success"></i>
                    Dịch vụ phổ biến nhất
                </h5>
            </div>
            <div class="card-body">
                <?php if($popularServices->count() > 0): ?>
                <div class="row g-3">
                    <?php $__currentLoopData = $popularServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="service-card text-center p-4 rounded-3 h-100" style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%); border: 2px solid rgba(16, 185, 129, 0.2); transition: all 0.3s ease;">
                            <div class="service-rank position-absolute top-0 start-0 m-2">
                                <span class="badge bg-success rounded-pill">#<?php echo e($index + 1); ?></span>
                            </div>
                            <div class="service-icon mb-3">
                                <div class="icon-wrapper mx-auto" style="width: 60px; height: 60px; background: var(--success-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-box fa-2x text-white"></i>
                                </div>
                            </div>
                            <h6 class="fw-bold mb-2"><?php echo e($service->name); ?></h6>
                            <div class="mb-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-users me-1"></i>
                                    <?php echo e($service->customer_services_count); ?> khách hàng
                                </span>
                            </div>
                            <?php if($service->category): ?>
                            <small class="text-muted"><?php echo e($service->category->name); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-chart-bar fa-3x text-muted opacity-50"></i>
                    </div>
                    <h5 class="text-muted">Chưa có dữ liệu thống kê</h5>
                    <p class="text-muted mb-0">Dữ liệu sẽ hiển thị khi có khách hàng sử dụng dịch vụ</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Content Posts Status -->
<div class="row g-4 mt-2">
    <div class="col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2 text-info"></i>
                        Bài đăng sắp tới (24h)
                    </h5>
                    <a href="<?php echo e(route('admin.content-scheduler.index')); ?>" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if($upcomingPosts->count() > 0): ?>
                <div class="post-list">
                    <?php $__currentLoopData = $upcomingPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="post-item d-flex align-items-center p-3 rounded-3 mb-3" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.05) 100%); border-left: 4px solid var(--info-gradient);">
                        <div class="post-icon me-3">
                            <div class="icon-wrapper" style="width: 45px; height: 45px; background: var(--info-gradient); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-calendar text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold mb-1"><?php echo e($post->title); ?></div>
                            <div class="d-flex align-items-center text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                <span class="me-3"><?php echo e($post->scheduled_at->format('d/m/Y H:i')); ?></span>
                                <i class="fas fa-users me-1"></i>
                                <span><?php echo e($post->target_groups_string); ?></span>
                            </div>
                            <div class="mt-1">
                                <span class="badge bg-info">
                                    <?php echo e($post->scheduled_at->diffForHumans()); ?>

                                </span>
                            </div>
                        </div>
                        <div class="ms-2">
                            <a href="<?php echo e(route('admin.content-scheduler.show', $post)); ?>"
                                class="btn btn-sm btn-outline-info rounded-pill"
                                data-bs-toggle="tooltip"
                                title="Xem chi tiết">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-calendar-check fa-2x text-success opacity-50"></i>
                    </div>
                    <h6 class="text-muted">Không có bài đăng nào trong 24h tới</h6>
                    <p class="text-muted mb-0 small">Lịch đăng bài trống</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        Bài đăng quá hạn
                    </h5>
                    <a href="<?php echo e(route('admin.content-scheduler.index', ['status' => 'scheduled'])); ?>" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if($overduePosts->count() > 0): ?>
                <div class="post-list">
                    <?php $__currentLoopData = $overduePosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="post-item d-flex align-items-center p-3 rounded-3 mb-3" style="background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%); border-left: 4px solid var(--danger-gradient);">
                        <div class="post-icon me-3">
                            <div class="icon-wrapper" style="width: 45px; height: 45px; background: var(--danger-gradient); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-exclamation text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold mb-1"><?php echo e($post->title); ?></div>
                            <div class="d-flex align-items-center text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                <span class="me-3">Đã quá <?php echo e($post->scheduled_at->diffForHumans()); ?></span>
                                <i class="fas fa-users me-1"></i>
                                <span><?php echo e($post->target_groups_string); ?></span>
                            </div>
                            <div class="mt-1">
                                <span class="badge bg-danger">
                                    Quá hạn
                                </span>
                            </div>
                        </div>
                        <div class="ms-2">
                            <a href="<?php echo e(route('admin.content-scheduler.edit', $post)); ?>"
                                class="btn btn-sm btn-outline-danger rounded-pill"
                                data-bs-toggle="tooltip"
                                title="Chỉnh sửa">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-2x text-success opacity-50"></i>
                    </div>
                    <h6 class="text-muted">Không có bài đăng quá hạn</h6>
                    <p class="text-muted mb-0 small">Lịch đăng bài được quản lý tốt</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mt-2">
    <div class="col-12">
        <div class="card ">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Thao tác nhanh
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="<?php echo e(route('admin.customers.create')); ?>" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span class="fw-bold">Thêm khách hàng</span>
                            <small class="text-white-50 mt-1">Khách hàng mới</small>
                        </a>
                    </div>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="<?php echo e(route('admin.service-packages.create')); ?>" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-box fa-2x mb-2"></i>
                            <span class="fw-bold">Thêm gói dịch vụ</span>
                            <small class="text-white-50 mt-1">Sản phẩm mới</small>
                        </a>
                    </div>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="<?php echo e(route('admin.customer-services.create')); ?>" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-link fa-2x mb-2"></i>
                            <span class="fw-bold">Gán dịch vụ</span>
                            <small class="text-white-50 mt-1">Kích hoạt dịch vụ</small>
                        </a>
                    </div>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="<?php echo e(route('admin.content-scheduler.create')); ?>" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                            <span class="fw-bold">Tạo bài đăng</span>
                            <small class="text-white-50 mt-1">Lên lịch đăng</small>
                        </a>
                    </div>

                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="<?php echo e(route('admin.reports.profit')); ?>" class="btn btn-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <span class="fw-bold">Báo cáo</span>
                            <small class="text-white-50 mt-1">Lợi nhuận</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Add hover effects to service cards
    document.addEventListener('DOMContentLoaded', function() {
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
                this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = 'none';
            });
        });

        // Add click animation to quick action buttons
        const quickActionBtns = document.querySelectorAll('.card-body .btn');
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Auto-refresh dashboard data every 2 minutes
        setInterval(() => {
            // Only refresh if user is active
            const lastActivity = localStorage.getItem('lastActivity') || Date.now();
            if (Date.now() - lastActivity < 120000) { // 2 minutes
                // Refresh specific sections instead of full page
                refreshDashboardData();
            }
        }, 120000);

        function refreshDashboardData() {
            // This would typically make AJAX calls to refresh specific sections
            // For now, we'll just show a subtle indicator that data is being refreshed
            const indicator = document.createElement('div');
            indicator.innerHTML = '<i class="fas fa-sync-alt fa-spin me-2"></i>Đang cập nhật...';
            indicator.className = 'position-fixed top-0 end-0 m-3 p-2 bg-primary text-white rounded-3 small';
            indicator.style.zIndex = '9999';
            document.body.appendChild(indicator);

            setTimeout(() => {
                indicator.remove();
            }, 2000);
        }

        // Track user activity for auto-refresh
        let activityTimer;
        ['mousemove', 'keypress', 'scroll', 'click'].forEach(event => {
            document.addEventListener(event, () => {
                localStorage.setItem('lastActivity', Date.now());
            });
        });
    });
</script>

<style>
    /* 5-column layout */
    .col-xl-2-4 {
        flex: 0 0 auto;
        width: 20%;
    }

    @media (max-width: 1199.98px) {
        .col-xl-2-4 {
            width: 25%;
        }
    }

    @media (max-width: 991.98px) {
        .col-xl-2-4 {
            width: 50%;
        }
    }

    @media (max-width: 575.98px) {
        .col-xl-2-4 {
            width: 100%;
        }
    }
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>