

<?php $__env->startSection('title', 'Quản lý cộng tác viên'); ?>
<?php $__env->startSection('page-title', 'Quản lý cộng tác viên'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo e($stats['total']); ?></h5>
                            <p class="card-text">Tổng cộng tác viên</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo e($stats['active']); ?></h5>
                            <p class="card-text">Đang hoạt động</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-concierge-bell fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo e($stats['total_services']); ?></h5>
                            <p class="card-text">Tổng dịch vụ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo e($stats['total_accounts']); ?></h5>
                            <p class="card-text">Tổng tài khoản</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        Danh sách cộng tác viên
                    </h5>
                </div>
                <div class="col-auto">
                    <a href="<?php echo e(route('admin.collaborators.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Thêm cộng tác viên
                    </a>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Search and Filter -->
            <form method="GET" class="row g-3 mb-4">
                <div class="col-md-4">
                    <input type="text"
                        class="form-control"
                        name="search"
                        value="<?php echo e(request('search')); ?>"
                        placeholder="Tìm kiếm tên, mã, email, SĐT...">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="sort_by" class="form-select">
                        <option value="created_at" <?php echo e(request('sort_by') === 'created_at' ? 'selected' : ''); ?>>Ngày tạo</option>
                        <option value="name" <?php echo e(request('sort_by') === 'name' ? 'selected' : ''); ?>>Tên</option>
                        <option value="collaborator_code" <?php echo e(request('sort_by') === 'collaborator_code' ? 'selected' : ''); ?>>Mã</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="sort_direction" class="form-select">
                        <option value="desc" <?php echo e(request('sort_direction') === 'desc' ? 'selected' : ''); ?>>Giảm dần</option>
                        <option value="asc" <?php echo e(request('sort_direction') === 'asc' ? 'selected' : ''); ?>>Tăng dần</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>

            <!-- Table -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mã CTV</th>
                            <th>Tên cộng tác viên</th>
                            <th>Liên hệ</th>
                            <th>Dịch vụ</th>
                            <th>Tổng giá trị</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $collaborators; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $collaborator): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <span class="badge bg-secondary"><?php echo e($collaborator->collaborator_code); ?></span>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo e($collaborator->name); ?></strong>
                                    <?php if($collaborator->notes): ?>
                                    <br><small class="text-muted"><?php echo e(Str::limit($collaborator->notes, 30)); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if($collaborator->email): ?>
                                <div><i class="fas fa-envelope me-1"></i> <?php echo e($collaborator->email); ?></div>
                                <?php endif; ?>
                                <?php if($collaborator->phone): ?>
                                <div><i class="fas fa-phone me-1"></i> <?php echo e($collaborator->phone); ?></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($collaborator->active_services_count); ?> dịch vụ</span>
                                <br>
                                <small class="text-muted"><?php echo e($collaborator->total_accounts); ?> tài khoản</small>
                            </td>
                            <td>
                                <strong class="text-success"><?php echo e(number_format($collaborator->total_value, 0, '.', ',')); ?> VND</strong>
                            </td>
                            <td>
                                <?php if($collaborator->status === 'active'): ?>
                                <span class="badge bg-success">Hoạt động</span>
                                <?php else: ?>
                                <span class="badge bg-danger">Không hoạt động</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($collaborator->created_at->format('d/m/Y')); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.collaborators.show', $collaborator)); ?>"
                                        class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.collaborators.edit', $collaborator)); ?>"
                                        class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="<?php echo e(route('admin.collaborators.destroy', $collaborator)); ?>"
                                        class="d-inline"
                                        onsubmit="return confirm('Bạn có chắc muốn xóa cộng tác viên này?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Chưa có cộng tác viên nào</p>
                                <a href="<?php echo e(route('admin.collaborators.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Thêm cộng tác viên đầu tiên
                                </a>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($collaborators->hasPages()): ?>
            <div class="d-flex justify-content-center">
                <?php echo e($collaborators->withQueryString()->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/collaborators/index.blade.php ENDPATH**/ ?>