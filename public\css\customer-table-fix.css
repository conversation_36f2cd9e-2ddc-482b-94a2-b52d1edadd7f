/* Customer Table Layout Fix - <PERSON><PERSON><PERSON> bả<PERSON> cột hành động hiển thị đầy đủ */

/* Container tối ưu với width cân bằng */
.table-container {
    width: 100%;
    max-width: 100% !important; /* THAY ĐỔI LẠI VỀ 100% */
    overflow: visible !important;
    position: relative;
}

/* Bảng khách hàng với width tối ưu cho màn hình 1920x1080 */
.customers-table {
    width: 100%;
    max-width: 100%;
    table-layout: fixed;
    margin-bottom: 0;
    border-collapse: collapse;
    font-size: 0.75rem;
}

.customers-table th,
.customers-table td {
    vertical-align: middle;
    padding: 0.35rem 0.25rem !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border: 1px solid #dee2e6;
}

/* Định nghĩa width tối ưu cho từng cột - TỔNG CHUẨN 100% */
.customers-table th:nth-child(1), /* <PERSON><PERSON> */
.customers-table td:nth-child(1) {
    width: 8% !important; /* <PERSON><PERSON><PERSON> từ 6% lên 8% */
    min-width: 70px !important;
    max-width: 90px !important;
}

.customers-table th:nth-child(2), /* <PERSON>h<PERSON><PERSON> hàng */
.customers-table td:nth-child(2) {
    width: 20% !important; /* Tăng từ 17% lên 20% */
    min-width: 160px !important;
    max-width: 220px !important;
}

.customers-table th:nth-child(3), /* Liên hệ */
.customers-table td:nth-child(3) {
    width: 18% !important; /* Tăng từ 15% lên 18% */
    min-width: 140px !important;
    max-width: 200px !important;
}

.customers-table th:nth-child(4), /* Dịch vụ */
.customers-table td:nth-child(4) {
    width: 14% !important; /* Tăng từ 12% lên 14% */
    min-width: 110px !important;
    max-width: 150px !important;
    text-align: center;
}

.customers-table th:nth-child(5), /* Ngày tạo */
.customers-table td:nth-child(5) {
    width: 12% !important; /* Tăng từ 10% lên 12% */
    min-width: 100px !important;
    max-width: 130px !important;
    text-align: center;
}

.customers-table th:nth-child(6), /* Thao tác */
.customers-table td:nth-child(6) {
    width: 28% !important; /* GIẢM từ 40% xuống 28% để loại bỏ khoảng trống */
    min-width: 280px !important; /* Giữ nguyên min-width */
    max-width: 350px !important; /* Giảm max-width */
    text-align: center !important;
    white-space: nowrap !important;
    overflow: visible !important;
    position: relative !important; /* THAY ĐỔI TỪ STICKY VỀ RELATIVE */
    padding: 8px 12px !important; /* Giảm padding */
    background: rgba(255, 255, 255, 0.98) !important;
    border-left: 2px solid #007bff !important;
    z-index: 10 !important;
}

/* Đảm bảo btn-group trong cột thao tác hiển thị đầy đủ và căn giữa */
.customers-table .btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 6px !important; /* Tăng gap để tránh nút dính nhau */
    justify-content: space-evenly !important; /* THAY ĐỔI: căn đều thay vì căn giữa */
    align-items: center !important;
    width: 100% !important;
    min-width: 260px !important; /* Giảm min-width */
    max-width: 300px !important; /* Thêm max-width */
    padding: 6px 8px !important; /* Tăng padding để tạo khoảng cách */
    position: relative !important;
    z-index: 15 !important;
    margin: 0 auto !important; /* Căn giữa button group */
}

.customers-table .btn-group .btn {
    padding: 6px 10px !important; /* Tăng padding */
    font-size: 0.7rem !important; /* Giảm font size một chút */
    line-height: 1.3 !important;
    min-width: 38px !important; /* Giảm min-width */
    max-width: 55px !important; /* Giảm max-width */
    height: 34px !important; /* Giảm height */
    border-radius: 5px !important;
    margin: 0 !important; /* Loại bỏ margin vì đã có gap */
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    flex-grow: 1 !important; /* THÊM: cho phép nút mở rộng đều */
    position: relative !important;
    z-index: 16 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid !important;
}

.customers-table .btn-group .btn i {
    font-size: 0.8rem !important;
    width: auto !important;
    margin: 0 !important;
}

/* Đặc biệt đảm bảo nút xóa không bị ẩn - HIỂN THỊ HOÀN TOÀN */
.customers-table .btn-group .btn:last-child,
.customers-table .btn-group .btn.btn-outline-danger {
    position: relative !important;
    z-index: 30 !important; /* Tăng z-index cao nhất */
    background: rgba(255, 255, 255, 0.98) !important;
    border: 2px solid #dc3545 !important;
    color: #dc3545 !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.25) !important;
    min-width: 40px !important; /* Đảm bảo width tối thiểu */
    flex-shrink: 0 !important; /* Không cho phép thu nhỏ */
    flex-grow: 0 !important; /* Không cho phép mở rộng */
    margin-left: auto !important; /* Đẩy về cuối cùng */
}

.customers-table .btn-group .btn:last-child:hover,
.customers-table .btn-group .btn.btn-outline-danger:hover {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
}

/* Tooltip cho các nút hành động */
.customers-table .btn[title] {
    position: relative;
}

/* Container responsive với full width */
.table-responsive {
    border-radius: 0.375rem;
    overflow-x: auto !important; /* THAY ĐỔI LẠI VỀ AUTO để có scroll khi cần */
    overflow-y: visible !important;
    max-width: 100% !important; /* THAY ĐỔI LẠI VỀ 100% */
    width: 100% !important;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

.table-responsive::-webkit-scrollbar {
    height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Đảm bảo text truncation phù hợp */
.customers-table td:nth-child(2) {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
}

.customers-table td:nth-child(3) {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
}

/* Badge trong cột dịch vụ */
.customers-table .badge {
    font-size: 0.6rem !important;
    padding: 2px 4px !important;
    margin: 1px !important;
}

/* Avatar styling */
.customers-table .avatar-initial {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
    flex-shrink: 0;
}

/* Responsive breakpoints */
@media (max-width: 1600px) {
    .customers-table {
        font-size: 0.7rem;
    }
    .customers-table th,
    .customers-table td {
        padding: 0.3rem 0.2rem !important;
    }
    .customers-table .btn-group .btn {
        padding: 4px 8px !important;
        font-size: 0.65rem !important;
        min-width: 38px !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        min-width: 240px !important;
    }
}

@media (max-width: 1400px) {
    .customers-table {
        font-size: 0.65rem;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 35% !important;
        min-width: 220px !important;
    }
    .customers-table .btn-group .btn {
        min-width: 35px !important;
        padding: 3px 6px !important;
    }
}

@media (max-width: 1200px) {
    /* Ẩn cột liên hệ trên tablet */
    .customers-table th:nth-child(3),
    .customers-table td:nth-child(3) {
        display: none;
    }

    /* Điều chỉnh width cho các cột còn lại */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 8% !important;
    }
    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 25% !important;
    }
    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 15% !important;
    }
    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        width: 12% !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 40% !important;
        min-width: 200px !important;
    }
}

@media (max-width: 768px) {
    /* Ẩn cột ngày tạo trên mobile */
    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        display: none;
    }

    /* Điều chỉnh width cho mobile */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 15% !important;
    }
    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 40% !important;
    }
    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 20% !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 25% !important;
        min-width: 120px !important;
    }

    /* Giảm kích thước nút trên mobile */
    .customers-table .btn-group .btn {
        padding: 2px 3px !important;
        font-size: 0.55rem !important;
        min-width: 24px !important;
    }
}

/* Đảm bảo dropdown menu hiển thị đúng trong bảng */
.customers-table .dropdown-menu {
    z-index: 1050 !important;
    position: absolute !important;
    min-width: 160px !important;
    font-size: 0.75rem !important;
}

.customers-table .dropdown-item {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
}

/* Fix cho trường hợp bảng quá rộng */
@media (min-width: 1920px) {
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 35% !important;
        min-width: 320px !important;
    }

    .customers-table .btn-group .btn {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
        min-width: 45px !important;
        height: 38px !important;
    }
}

/* Bảo vệ form elements khỏi bị ảnh hưởng bởi table CSS */
.form-select,
.form-control,
.btn:not(.customers-table .btn) {
    position: static !important;
    z-index: auto !important;
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
    overflow: visible !important;
    white-space: normal !important;
    text-overflow: clip !important;
}

/* Đảm bảo dropdown menu hiển thị đúng */
.dropdown-menu:not(.customers-table .dropdown-menu) {
    z-index: 1050 !important;
    position: absolute !important;
    min-width: 160px !important;
    max-width: none !important;
    overflow: visible !important;
}

/* Filter form không bị ảnh hưởng */
.card .form-select,
.card .form-control,
.card .btn {
    font-size: 0.875rem !important;
    padding: 0.375rem 0.75rem !important;
    line-height: 1.5 !important;
    border-radius: 0.375rem !important;
    height: auto !important;
}

/* Reset button styling trong filter */
.btn-primary:not(.customers-table .btn),
.btn-outline-primary:not(.customers-table .btn),
.btn-success:not(.customers-table .btn),
.btn-outline-success:not(.customers-table .btn),
.btn-danger:not(.customers-table .btn),
.btn-outline-danger:not(.customers-table .btn),
.btn-outline-secondary:not(.customers-table .btn) {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    border-radius: 0.375rem !important;
    min-width: auto !important;
    max-width: none !important;
    height: auto !important;
    margin: 0 !important;
    flex-grow: 0 !important;
    flex-shrink: 1 !important;
}
