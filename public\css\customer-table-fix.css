/* Customer Table Layout Fix - <PERSON><PERSON><PERSON> bả<PERSON> cột hành động hiển thị đầy đủ */

/* Container tối ưu để đảm bảo bảng không tràn */
.table-container {
    width: 100%;
    max-width: 100%;
    overflow: visible !important;
    position: relative;
}

/* B<PERSON>ng khách hàng với width tối ưu cho màn hình 1920x1080 */
.customers-table {
    width: 100%;
    max-width: 100%;
    table-layout: fixed;
    margin-bottom: 0;
    border-collapse: collapse;
    font-size: 0.75rem;
}

.customers-table th,
.customers-table td {
    vertical-align: middle;
    padding: 0.35rem 0.25rem !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border: 1px solid #dee2e6;
}

/* Định nghĩa width tối ưu cho từng cột - tổng ~1800px để fit trong 1920px với padding */
.customers-table th:nth-child(1), /* <PERSON><PERSON> */
.customers-table td:nth-child(1) {
    width: 8% !important;
    min-width: 80px !important;
    max-width: 100px !important;
}

.customers-table th:nth-child(2), /* <PERSON><PERSON><PERSON><PERSON> hàng */
.customers-table td:nth-child(2) {
    width: 20% !important;
    min-width: 180px !important;
    max-width: 250px !important;
}

.customers-table th:nth-child(3), /* Liên hệ */
.customers-table td:nth-child(3) {
    width: 18% !important;
    min-width: 160px !important;
    max-width: 220px !important;
}

.customers-table th:nth-child(4), /* Dịch vụ */
.customers-table td:nth-child(4) {
    width: 15% !important;
    min-width: 120px !important;
    max-width: 150px !important;
    text-align: center;
}

.customers-table th:nth-child(5), /* Ngày tạo */
.customers-table td:nth-child(5) {
    width: 12% !important;
    min-width: 100px !important;
    max-width: 120px !important;
    text-align: center;
}

.customers-table th:nth-child(6), /* Thao tác - QUAN TRỌNG NHẤT */
.customers-table td:nth-child(6) {
    width: 27% !important;
    min-width: 220px !important;
    max-width: 300px !important;
    text-align: center !important;
    white-space: nowrap !important;
    overflow: visible !important;
    position: relative !important;
}

/* Đảm bảo btn-group trong cột thao tác hiển thị đầy đủ */
.customers-table .btn-group {
    display: flex;
    flex-wrap: nowrap;
    gap: 2px;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.customers-table .btn-group .btn {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
    line-height: 1.2 !important;
    min-width: 30px !important;
    border-radius: 4px !important;
    margin: 0 1px !important;
    white-space: nowrap !important;
}

.customers-table .btn-group .btn i {
    font-size: 0.7rem !important;
    width: auto !important;
}

/* Tooltip cho các nút hành động */
.customers-table .btn[title] {
    position: relative;
}

/* Container responsive để đảm bảo không có horizontal scroll */
.table-responsive {
    border-radius: 0.375rem;
    overflow-x: auto;
    overflow-y: visible;
    max-width: 100%;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

.table-responsive::-webkit-scrollbar {
    height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Đảm bảo text truncation phù hợp */
.customers-table td:nth-child(2) {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
}

.customers-table td:nth-child(3) {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
}

/* Badge trong cột dịch vụ */
.customers-table .badge {
    font-size: 0.6rem !important;
    padding: 2px 4px !important;
    margin: 1px !important;
}

/* Avatar styling */
.customers-table .avatar-initial {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
    flex-shrink: 0;
}

/* Responsive breakpoints */
@media (max-width: 1600px) {
    .customers-table {
        font-size: 0.7rem;
    }
    .customers-table th,
    .customers-table td {
        padding: 0.3rem 0.2rem !important;
    }
    .customers-table .btn-group .btn {
        padding: 2px 4px !important;
        font-size: 0.6rem !important;
    }
}

@media (max-width: 1400px) {
    .customers-table {
        font-size: 0.65rem;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        min-width: 200px !important;
    }
}

@media (max-width: 1200px) {
    /* Ẩn cột liên hệ trên tablet */
    .customers-table th:nth-child(3),
    .customers-table td:nth-child(3) {
        display: none;
    }

    /* Điều chỉnh width cho các cột còn lại */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 10% !important;
    }
    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 35% !important;
    }
    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 20% !important;
    }
    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        width: 12% !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 23% !important;
        min-width: 180px !important;
    }
}

@media (max-width: 768px) {
    /* Ẩn cột ngày tạo trên mobile */
    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        display: none;
    }

    /* Điều chỉnh width cho mobile */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 15% !important;
    }
    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 40% !important;
    }
    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 20% !important;
    }
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 25% !important;
        min-width: 120px !important;
    }

    /* Giảm kích thước nút trên mobile */
    .customers-table .btn-group .btn {
        padding: 2px 3px !important;
        font-size: 0.55rem !important;
        min-width: 24px !important;
    }
}

/* Đảm bảo dropdown menu hiển thị đúng trong bảng */
.customers-table .dropdown-menu {
    z-index: 1050 !important;
    position: absolute !important;
    min-width: 160px !important;
    font-size: 0.75rem !important;
}

.customers-table .dropdown-item {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
}

/* Fix cho trường hợp bảng quá rộng */
@media (min-width: 1920px) {
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 25% !important;
        min-width: 250px !important;
    }
}
