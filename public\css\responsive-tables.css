/* Responsive Table Optimization */
/* Tối <PERSON>u bảng cho màn hình nhỏ */

/* Prevent horizontal overflow completely */
html {
    overflow-x: hidden !important;
}

body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

/* Container optimization for full functionality */
.container,
.container-fluid {
    max-width: 100% !important;
    padding-left: 12px !important;
    padding-right: 12px !important;
}

.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.col,
[class*="col-"] {
    padding-left: 6px !important;
    padding-right: 6px !important;
}

.table-responsive {
    border-radius: 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
    min-height: 400px !important;
}

/* FUNCTIONAL DESIGN - SHOW ALL FEATURES ON SCREEN */
/* Restore full functionality visibility */

/* Header with full button visibility */
.card-header {
    padding: 12px 20px !important;
    min-height: 60px !important;
    max-width: 100% !important;
    overflow: visible !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card-header h5 {
    font-size: 1.1rem !important;
    margin-bottom: 2px !important;
    font-weight: 600 !important;
    color: white !important;
}

.card-header p,
.card-header small {
    font-size: 0.85rem !important;
    margin-bottom: 0 !important;
    opacity: 0.9;
    color: rgba(255,255,255,0.8) !important;
}

.card-header .icon-wrapper {
    display: inline-block !important; /* Show icons for better UX */
}

.card-header .btn {
    padding: 8px 16px !important;
    font-size: 0.9rem !important;
    border-radius: 6px !important;
    white-space: nowrap !important;
    min-width: 110px !important;
    margin-left: 8px !important;
    font-weight: 500 !important;
}

.card-header .btn i {
    font-size: 0.85rem !important;
    margin-right: 6px !important;
}

.card-header .d-flex {
    align-items: center !important;
    min-height: 40px !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
}

/* Ensure buttons are visible and clickable */
.card-header .btn-warning,
.card-header .btn-light {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ultra compact form spacing */
.card-body {
    padding: 10px 12px !important;
    max-width: 100% !important;
    overflow: hidden !important;
}

.form-label {
    font-size: 0.75rem !important;
    margin-bottom: 2px !important;
    font-weight: 500 !important;
}

.form-control,
.form-select {
    padding: 5px 8px !important;
    font-size: 0.8rem !important;
    border-radius: 3px !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.btn {
    border-radius: 3px !important;
    white-space: nowrap !important;
}

/* Search form optimization */
.search-form {
    max-width: 100% !important;
    overflow: hidden !important;
}

.search-form .row {
    margin: 0 !important;
}

.search-form .col {
    padding: 2px !important;
}

.search-form .form-control {
    min-width: 0 !important;
    width: 100% !important;
}

/* Ultra compact stats cards */
.row.mb-3 .card-body {
    padding: 6px 8px !important;
    text-align: center;
}

.row.mb-3 h4 {
    font-size: 1.1rem !important;
    margin-bottom: 2px !important;
}

.row.mb-3 small {
    font-size: 0.7rem !important;
}

/* Form spacing optimization */
.card-body {
    padding: 16px !important;
}

.form-label {
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.form-control,
.form-select {
    padding: 8px 12px;
    font-size: 0.9rem;
}

/* Button consistency - Ultra compact */
.btn-sm {
    padding: 3px 6px !important;
    font-size: 0.7rem !important;
}

.btn-group .btn {
    padding: 2px 4px !important;
    font-size: 0.65rem !important;
}

/* Quick action buttons */
.d-flex.gap-2 .btn {
    margin-right: 4px !important;
    white-space: nowrap;
}

.d-flex.flex-wrap.gap-2 .btn {
    margin-bottom: 4px !important;
    padding: 3px 8px !important;
    font-size: 0.75rem !important;
}

/* Stats and info cards - Ultra compact */
.bg-light .card-body {
    padding: 6px 12px !important;
}

.bg-light .text-muted {
    font-size: 0.8rem !important;
}

.bg-light .badge {
    font-size: 0.65rem !important;
    padding: 2px 6px !important;
}

/* Remove excessive margins */
.mb-3 {
    margin-bottom: 0.75rem !important;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

/* Table display optimization - SHOW ALL CONTENT */
.table td {
    padding: 8px 6px !important;
    font-size: 0.8rem !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #e9ecef !important;
    /* Remove overflow hidden to show full content */
}

/* Table structure with FULL functionality */
.table thead th {
    padding: 10px 8px !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    white-space: nowrap !important;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
}

.table {
    table-layout: fixed;
    width: 100%;
    margin-bottom: 0;
    max-width: 100% !important;
}

/* Allow text to show completely */
.customers-table td:nth-child(1) {
    white-space: nowrap;
}

.customers-table td:nth-child(2) {
    white-space: normal;
    word-wrap: break-word;
}

.customers-table td:nth-child(3) {
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3 !important;
}

.customers-table td:nth-child(4) {
    text-align: center;
    white-space: normal;
}

.customers-table td:nth-child(5) {
    text-align: center;
    white-space: nowrap;
}

.customers-table td:nth-child(6) {
    text-align: center;
    white-space: nowrap;
    padding: 8px 10px !important;
}

/* BALANCED column widths - show ALL functions */
.customers-table th:nth-child(1), /* Mã KH */
.customers-table td:nth-child(1) {
    width: 100px;
    min-width: 100px;
}

.customers-table th:nth-child(2), /* Khách hàng */
.customers-table td:nth-child(2) {
    width: 200px;
    min-width: 200px;
}

.customers-table th:nth-child(3), /* Liên hệ */
.customers-table td:nth-child(3) {
    width: 220px;
    min-width: 220px;
}

.customers-table th:nth-child(4), /* Dịch vụ */
.customers-table td:nth-child(4) {
    width: 150px;
    min-width: 150px;
    text-align: center;
}

.customers-table th:nth-child(5), /* Ngày tạo */
.customers-table td:nth-child(5) {
    width: 120px;
    min-width: 120px;
    text-align: center;
}

.customers-table th:nth-child(6), /* THAO TÁC - MOST IMPORTANT */
.customers-table td:nth-child(6) {
    width: 180px;
    min-width: 180px;
    text-align: center;
    padding: 8px 10px !important;
}
.customers-table td:nth-child(5) {
    width: 130px;
    min-width: 130px;
}

.customers-table th:nth-child(6), /* Thao tác */
.customers-table td:nth-child(6) {
    width: 120px;
    min-width: 120px;
    text-align: center;
}

/* Text overflow handling */
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

/* Allow contact info to wrap */
.customers-table td:nth-child(3) {
    white-space: normal;
    word-wrap: break-word;
}

/* Service column centers and badges */
.customers-table td:nth-child(4) {
    text-align: center;
}

.customers-table td:nth-child(4) .badge {
    font-size: 0.75rem;
    margin: 1px;
}

/* FULL FUNCTIONALITY ACTION BUTTONS */
.customers-table td:nth-child(6) .btn-group {
    white-space: nowrap;
    display: flex !important;
    gap: 4px !important;
    justify-content: center !important;
}

.customers-table td:nth-child(6) .btn {
    padding: 6px 10px !important;
    font-size: 0.8rem !important;
    border-radius: 4px !important;
    min-width: 35px !important;
    font-weight: 500 !important;
}

/* Specific action button colors */
.customers-table .btn-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
}

.customers-table .btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

.customers-table .btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

/* Action button icons */
.customers-table .btn i {
    font-size: 0.75rem !important;
}

/* Make sure text doesn't wrap in contact column */
.customers-table td:nth-child(3) {
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3 !important;
}

/* Service badges visible */
.customers-table td:nth-child(4) {
    text-align: center;
}

.customers-table td:nth-child(4) .badge {
    font-size: 0.7rem !important;
    margin: 1px 2px !important;
    padding: 3px 6px !important;
}

/* Email display in services */
.customers-table .text-muted span {
    max-width: 180px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: bottom;
}

/* ENHANCED HEADER BUTTONS - ALWAYS VISIBLE */
.card-header .d-flex.gap-1 .btn,
.card-header .d-flex .btn {
    padding: 10px 18px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
    white-space: nowrap !important;
    min-width: 120px !important;
    text-align: center !important;
    line-height: 1.2 !important;
    margin: 2px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    transition: all 0.2s ease !important;
}

/* THÊM KHÁCH HÀNG button - Make it prominent */
.card-header .btn-warning {
    background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%) !important;
    border: none !important;
    color: white !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

.card-header .btn-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid #dee2e6 !important;
    color: #495057 !important;
    font-weight: 600 !important;
}

.card-header .btn-warning:hover {
    background: linear-gradient(135deg, #e68900 0%, #e55a2b 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.card-header .btn-light:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

/* Icon spacing in header buttons */
.card-header .btn i {
    font-size: 0.85rem !important;
    margin-right: 8px !important;
}

/* Responsive header buttons */
@media (max-width: 768px) {
    .card-header .d-flex.gap-1 .btn,
    .card-header .d-flex .btn {
        padding: 8px 14px !important;
        font-size: 0.8rem !important;
        min-width: 100px !important;
    }
    
    .card-header .btn i {
        font-size: 0.75rem !important;
        margin-right: 6px !important;
    }
}

/* Specific button types */
.card-header .btn-warning {
    background-color: #f39c12 !important;
    border-color: #f39c12 !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3) !important;
}

.card-header .btn-light {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.card-header .btn-warning:hover {
    background-color: #e67e22 !important;
    border-color: #e67e22 !important;
    transform: translateY(-1px) !important;
}

.card-header .btn-light:hover {
    background-color: #e2e6ea !important;
    border-color: #dae0e5 !important;
    transform: translateY(-1px) !important;
}

/* Icon spacing in header buttons */
.card-header .btn i {
    font-size: 0.85rem !important;
    margin-right: 6px !important;
}

/* Responsive header buttons */
@media (max-width: 768px) {
    .card-header .d-flex.gap-1 .btn,
    .card-header .d-flex .btn {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
        min-width: 80px !important;
    }

    .card-header .btn i {
        font-size: 0.75rem !important;
        margin-right: 4px !important;
    }
}

@media (max-width: 1200px) {
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 100px;
        min-width: 100px;
    }

    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 160px;
        min-width: 150px;
    }

    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 80px;
        min-width: 80px;
    }

    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 100px;
        min-width: 100px;
    }
}

@media (max-width: 992px) {
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 80px;
        min-width: 80px;
    }

    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 200px;
        min-width: 180px;
    }

    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 70px;
        min-width: 70px;
    }

    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 80px;
        min-width: 80px;
    }

    .customers-table .btn-group .btn {
        padding: 3px 6px;
        font-size: 0.7rem;
    }
}

/* Ultra compact viewport optimization - NO HORIZONTAL SCROLL */
@media (max-width: 1400px) {
    .container,
    .container-fluid {
        max-width: 100vw !important;
        padding-left: 6px !important;
        padding-right: 6px !important;
    }

    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 80px;
        min-width: 80px;
    }

    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 120px;
        min-width: 120px;
    }

    .customers-table th:nth-child(3),
    .customers-table td:nth-child(3) {
        width: 130px;
        min-width: 130px;
    }

    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 70px;
        min-width: 70px;
    }

    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        width: 80px;
        min-width: 80px;
    }

    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 100px;
        min-width: 100px;
    }

    .table td,
    .table th {
        font-size: 0.7rem !important;
        padding: 3px 2px !important;
    }

    .card-header .btn {
        padding: 2px 4px !important;
        font-size: 0.65rem !important;
    }
}

@media (max-width: 1200px) {
    /* Even more compact for smaller screens */
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 70px;
        min-width: 70px;
    }

    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 110px;
        min-width: 110px;
    }

    .customers-table th:nth-child(3),
    .customers-table td:nth-child(3) {
        width: 120px;
        min-width: 120px;
    }

    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 60px;
        min-width: 60px;
    }

    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        width: 70px;
        min-width: 70px;
    }

    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 90px;
        min-width: 90px;
    }
}

/* ULTRA COMPACT NO HORIZONTAL SCROLL OPTIMIZATION */
/* Prevent any horizontal overflow at zoom 100% */

/* Force viewport constraints */
html {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

/* Ultra tight container spacing */
.container,
.container-fluid {
    max-width: calc(100vw - 10px) !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
}

/* Ultra compact table for no scroll */
@media (max-width: 1400px) {
    .customers-table th:nth-child(1),
    .customers-table td:nth-child(1) {
        width: 75px !important;
        min-width: 75px !important;
    }

    .customers-table th:nth-child(2),
    .customers-table td:nth-child(2) {
        width: 115px !important;
        min-width: 115px !important;
    }

    .customers-table th:nth-child(3),
    .customers-table td:nth-child(3) {
        width: 125px !important;
        min-width: 125px !important;
    }

    .customers-table th:nth-child(4),
    .customers-table td:nth-child(4) {
        width: 65px !important;
        min-width: 65px !important;
    }

    .customers-table th:nth-child(5),
    .customers-table td:nth-child(5) {
        width: 75px !important;
        min-width: 75px !important;
    }

    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 95px !important;
        min-width: 95px !important;
    }

    /* Ultra small fonts and padding */
    .table td,
    .table th {
        font-size: 0.65rem !important;
        padding: 2px 1px !important;
    }

    /* Ultra compact buttons */
    .btn-group .btn {
        padding: 1px 2px !important;
        font-size: 0.55rem !important;
    }

    .badge {
        font-size: 0.55rem !important;
        padding: 1px 3px !important;
    }
}

/* Force no overflow for all elements */
* {
    box-sizing: border-box !important;
}

.table-responsive {
    max-width: calc(100vw - 15px) !important;
}

.card {
    max-width: 100% !important;
    overflow: hidden !important;
}
