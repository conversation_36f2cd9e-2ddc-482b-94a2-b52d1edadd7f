/* CSS Override để fix các xung đột trong responsive-tables.css */

/* Reset tất cả các định nghĩa trùng lặp cho cột thao tác */
.customers-table th:nth-child(6),
.customers-table td:nth-child(6) {
    width: 26% !important;
    min-width: 240px !important;
    max-width: 320px !important;
    text-align: center !important;
    white-space: nowrap !important;
    overflow: visible !important;
    position: relative !important;
    padding: 8px 12px !important;
    background: rgba(255, 255, 255, 0.98) !important;
    border-left: 2px solid #e9ecef !important;
    z-index: 20 !important;
}

/* Đ<PERSON><PERSON> bảo các nút hành động hiển thị đầy đủ */
.customers-table .btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 3px !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    min-width: 220px !important;
    position: relative !important;
    z-index: 25 !important;
}

.customers-table .btn-group .btn {
    padding: 4px 8px !important;
    font-size: 0.7rem !important;
    line-height: 1.3 !important;
    min-width: 36px !important;
    max-width: 50px !important;
    border-radius: 4px !important;
    margin: 0 1px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    position: relative !important;
    z-index: 30 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Đặc biệt đảm bảo nút xóa LUÔN hiển thị và có thể truy cập */
.customers-table .btn-group .btn:last-child,
.customers-table .btn-group .btn.btn-outline-danger,
.customers-table .btn-group .btn[title*="Xóa"],
.customers-table .btn-group .btn.delete-btn {
    position: relative !important;
    z-index: 50 !important;
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid #dc3545 !important;
    color: #dc3545 !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-flex !important;
    pointer-events: auto !important;
}

/* Hover state cho nút xóa */
.customers-table .btn-group .btn:last-child:hover,
.customers-table .btn-group .btn.btn-outline-danger:hover,
.customers-table .btn-group .btn[title*="Xóa"]:hover,
.customers-table .btn-group .btn.delete-btn:hover {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
    z-index: 55 !important;
}

/* Icon trong nút xóa */
.customers-table .btn-group .btn:last-child i,
.customers-table .btn-group .btn.btn-outline-danger i,
.customers-table .btn-group .btn[title*="Xóa"] i,
.customers-table .btn-group .btn.delete-btn i {
    font-size: 0.75rem !important;
    color: inherit !important;
    opacity: 1 !important;
    display: inline-block !important;
}

/* Đảm bảo table responsive nhưng không ẩn cột thao tác */
.table-responsive {
    overflow-x: auto !important;
    overflow-y: visible !important;
    max-width: 100% !important;
    -webkit-overflow-scrolling: touch !important;
}

/* Container tối ưu */
.table-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow: visible !important;
    position: relative !important;
}

/* Reset xung đột về text overflow */
.customers-table td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    vertical-align: middle !important;
}

/* Cho phép cột tên khách hàng và liên hệ wrap text */
.customers-table td:nth-child(2),
.customers-table td:nth-child(3) {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
}
