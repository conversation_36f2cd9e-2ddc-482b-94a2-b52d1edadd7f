/* CSS Override để fix các xung đột trong responsive-tables.css */

/* Reset tất cả các định nghĩa trùng lặp cho cột thao tác */
.customers-table th:nth-child(6),
.customers-table td:nth-child(6) {
    width: 27% !important;
    min-width: 220px !important;
    max-width: 300px !important;
    text-align: center !important;
    white-space: nowrap !important;
    overflow: visible !important;
    position: relative !important;
    padding: 8px 10px !important;
}

/* Đ<PERSON><PERSON> bảo các nút hành động hiển thị đầy đủ */
.customers-table .btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 2px !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
}

.customers-table .btn-group .btn {
    padding: 3px 6px !important;
    font-size: 0.65rem !important;
    line-height: 1.2 !important;
    min-width: 30px !important;
    border-radius: 4px !important;
    margin: 0 1px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
}

/* Đảm bảo table responsive nhưng không ẩn cột thao tác */
.table-responsive {
    overflow-x: auto !important;
    overflow-y: visible !important;
    max-width: 100% !important;
    -webkit-overflow-scrolling: touch !important;
}

/* Container tối ưu */
.table-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow: visible !important;
    position: relative !important;
}

/* Reset xung đột về text overflow */
.customers-table td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    vertical-align: middle !important;
}

/* Cho phép cột tên khách hàng và liên hệ wrap text */
.customers-table td:nth-child(2),
.customers-table td:nth-child(3) {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
}
