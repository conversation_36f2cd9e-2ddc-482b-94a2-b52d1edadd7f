<?php $__env->startSection('title', '<PERSON>uản lý khách hàng'); ?>
<?php $__env->startSection('page-title', 'Quản lý khách hàng'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Container đ<PERSON> đảm bảo bảng không tràn */
    .table-container {
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        position: relative;
    }

    .customers-table {
        font-size: 0.75rem;
        width: 100%;
        max-width: 100%;
        table-layout: fixed;
        margin-bottom: 0;
        border-collapse: collapse;
    }
    .customers-table th,
    .customers-table td {
        vertical-align: middle;
        padding: 0.35rem 0.25rem !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .customers-table .text-truncate {
        max-width: 100px;
    }
    .table-responsive {
        border-radius: 0.375rem;
        overflow-x: auto;
        overflow-y: visible;
        max-width: 100%;
    }

    /* <PERSON><PERSON><PERSON> bảo actions column luôn hiển thị */
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        min-width: 160px; /* <PERSON>ảm bảo đủ chỗ cho 4 nút action */
        white-space: nowrap;
        text-align: center;
    }
    .dropdown-menu {
        min-width: 120px;
        font-size: 0.75rem;
    }
    .dropdown-item {
        padding: 0.25rem 0.75rem;
    }
    .dropdown-item i {
        width: 12px;
    }

    /* Định nghĩa width cố định cho các cột để fit trong màn hình 1920px */
    .customers-table th:nth-child(1) { width: 8%; } /* Mã KH */
    .customers-table th:nth-child(2) { width: 20%; } /* Khách hàng */
    .customers-table th:nth-child(3) { width: 16%; } /* Liên hệ */
    .customers-table th:nth-child(4) { width: 18%; } /* Dịch vụ */
    .customers-table th:nth-child(5) { width: 10%; } /* Ngày tạo */
    .customers-table th:nth-child(6) { width: 28%; } /* Thao tác - tăng để đảm bảo hiển thị */

    /* Styling cho btn-group trong cột thao tác */
    .customers-table .btn-group {
        display: flex;
        flex-wrap: nowrap;
        gap: 0px;
        justify-content: center;
    }

    .customers-table .btn-group .btn {
        padding: 2px 4px !important;
        font-size: 0.6rem;
        line-height: 1;
        min-width: auto;
        border-radius: 3px !important;
        margin: 0 1px;
    }

    .customers-table .btn-group .btn i {
        font-size: 0.65rem;
    }

    /* Tối ưu kích thước avatar */
    .customers-table .avatar-initial {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.7rem !important;
    }

    /* Tối ưu badge dịch vụ */
    .customers-table .badge {
        font-size: 0.6rem;
        padding: 2px 4px;
    }

    /* Responsive cho màn hình nhỏ hơn 1600px */
    @media (max-width: 1600px) {
        .customers-table {
            font-size: 0.7rem;
        }
        .customers-table th,
        .customers-table td {
            padding: 0.3rem 0.2rem !important;
        }

        /* Điều chỉnh width cho màn hình 1600px */
        .customers-table th:nth-child(1) { width: 8%; } /* Mã KH */
        .customers-table th:nth-child(2) { width: 22%; } /* Khách hàng */
        .customers-table th:nth-child(3) { width: 15%; } /* Liên hệ */
        .customers-table th:nth-child(4) { width: 20%; } /* Dịch vụ */
        .customers-table th:nth-child(5) { width: 10%; } /* Ngày tạo */
        .customers-table th:nth-child(6) { width: 25%; } /* Thao tác */
    }

    /* Responsive cho tablet */
    @media (max-width: 1200px) {
        .customers-table {
            font-size: 0.65rem;
        }
        .customers-table th,
        .customers-table td {
            padding: 0.25rem 0.15rem !important;
        }

        /* Ẩn cột liên hệ trên tablet */
        .customers-table th:nth-child(3),
        .customers-table td:nth-child(3) {
            display: none;
        }

        /* Điều chỉnh width cho tablet */
        .customers-table th:nth-child(1) { width: 10%; } /* Mã KH */
        .customers-table th:nth-child(2) { width: 35%; } /* Khách hàng */
        .customers-table th:nth-child(4) { width: 22%; } /* Dịch vụ */
        .customers-table th:nth-child(5) { width: 10%; } /* Ngày tạo */
        .customers-table th:nth-child(6) { width: 23%; } /* Thao tác */
    }

    /* Responsive cho mobile */
    @media (max-width: 768px) {
        .customers-table {
            font-size: 0.7rem;
        }
        .customers-table th,
        .customers-table td {
            padding: 0.25rem 0.15rem !important;
        }

        /* Ẩn cột liên hệ và ngày tạo trên mobile */
        .customers-table th:nth-child(3),
        .customers-table td:nth-child(3),
        .customers-table th:nth-child(5),
        .customers-table td:nth-child(5) {
            display: none;
        }

        /* Điều chỉnh width cho mobile */
        .customers-table th:nth-child(1) { width: 20%; } /* Mã KH */
        .customers-table th:nth-child(2) { width: 45%; } /* Khách hàng */
        .customers-table th:nth-child(4) { width: 15%; } /* Dịch vụ */
        .customers-table th:nth-child(6) { width: 20%; } /* Thao tác */

        /* Giảm kích thước nút trên mobile */
        .customers-table .btn-sm {
            padding: 2px 3px !important;
            font-size: 0.6rem;
        }

        .customers-table .btn-group .btn {
            padding: 2px 3px !important;
        }

        .customers-table .btn-group .btn i {
            font-size: 0.6rem !important;
        }
    }

    /* CRITICAL FIX: Đảm bảo cột thao tác luôn hiển thị */
    .customers-table th:nth-child(6),
    .customers-table td:nth-child(6) {
        width: 27% !important;
        min-width: 220px !important;
        max-width: 300px !important;
        text-align: center !important;
        white-space: nowrap !important;
        overflow: visible !important;
        position: sticky !important;
        right: 0 !important;
        background: white !important;
        z-index: 10 !important;
        border-left: 2px solid #e9ecef !important;
    }

    /* Đảm bảo button group hiển thị đầy đủ */
    .customers-table .btn-group {
        display: flex !important;
        flex-wrap: nowrap !important;
        gap: 2px !important;
        justify-content: center !important;
        width: 100% !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row g-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0 fw-bold text-white">Quản lý khách hàng</h5>
                        <small class="text-light opacity-75">Danh sách và quản lý thông tin khách hàng</small>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-light btn-sm shadow-sm" data-bs-toggle="modal"
                            data-bs-target="#quickAddModal">
                            <i class="fas fa-plus me-1"></i> Thêm nhanh
                        </button>
                        <a href="<?php echo e(route('admin.customers.create', ['page' => request('page', 1), 'search' => request('search')])); ?>"
                            class="btn btn-success btn-sm shadow-sm fw-bold">
                            <i class="fas fa-user-plus me-1"></i> Thêm khách hàng
                        </a>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Enhanced Filters -->
                <div class="card border-0 bg-light mb-2">
                    <div class="card-body">
                        <form method="GET" class="row g-2 align-items-end">
                            <div class="col-lg-4 col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-search text-primary"></i> Tìm kiếm
                                </label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Tên, email, phone, mã KH..."
                                    value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-box text-success"></i> Gói dịch vụ
                                </label>
                                <select name="service_package_id" class="form-select">
                                    <option value="">Tất cả gói dịch vụ</option>
                                    <?php $__currentLoopData = $servicePackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($package->id); ?>"
                                        <?php echo e(request('service_package_id') == $package->id ? 'selected' : ''); ?>>
                                        <?php echo e($package->name); ?>

                                    </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-lg-2 col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-toggle-on text-info"></i> Trạng thái
                                </label>
                                <select name="service_status" class="form-select">
                                    <option value="">Tất cả</option>
                                    <option value="active"
                                        <?php echo e(request('service_status') === 'active' ? 'selected' : ''); ?>>
                                        Hoạt động
                                    </option>
                                    <option value="expired"
                                        <?php echo e(request('service_status') === 'expired' ? 'selected' : ''); ?>>
                                        Hết hạn
                                    </option>
                                </select>
                            </div>
                            <div class="col-lg-2 col-md-6">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search"></i> Lọc
                                    </button>
                                    <?php if(request()->hasAny(['search', 'service_package_id', 'service_status'])): ?>
                                    <a href="<?php echo e(route('admin.customers.index')); ?>"
                                        class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times"></i> Xóa
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="d-flex flex-wrap gap-1 mb-2">
                    <a href="<?php echo e(route('admin.customers.index', ['date_from' => now()->startOfMonth()->format('Y-m-d'), 'date_to' => now()->endOfMonth()->format('Y-m-d')])); ?>"
                        class="btn btn-sm <?php echo e(request('date_from') === now()->startOfMonth()->format('Y-m-d') && request('date_to') === now()->endOfMonth()->format('Y-m-d') ? 'btn-primary' : 'btn-outline-primary'); ?>">
                        <i class="fas fa-calendar"></i> Tháng này
                    </a>
                    <a href="<?php echo e(route('admin.customers.index', ['service_status' => 'active'])); ?>"
                        class="btn btn-sm <?php echo e(request('service_status') === 'active' ? 'btn-success' : 'btn-outline-success'); ?>">
                        <i class="fas fa-check-circle"></i> DV hoạt động
                    </a>
                    <a href="<?php echo e(route('admin.customers.index', ['service_status' => 'expired'])); ?>"
                        class="btn btn-sm <?php echo e(request('service_status') === 'expired' ? 'btn-danger' : 'btn-outline-danger'); ?>">
                        <i class="fas fa-times-circle"></i> DV hết hạn
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Info -->
<div class="row mb-2">
    <div class="col-12">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted small">
                        <i class="fas fa-info-circle text-primary"></i>
                        <strong class="text-dark"><?php echo e(number_format($customers->total())); ?></strong> khách hàng
                        <?php if($customers->total() > 0): ?>
                        (<?php echo e($customers->firstItem()); ?>-<?php echo e($customers->lastItem()); ?>)
                        <?php endif; ?>
                    </div>
                    <?php if($customers->total() > 0): ?>
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> <?php echo e(now()->format('H:i d/m/Y')); ?>

                    </small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customers Table -->
<?php if($customers->count() > 0): ?>
<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0 customers-table">
                    <thead class="bg-light">
                        <tr>
                            <th class="py-2 px-2 fw-bold text-primary">Mã KH</th>
                            <th class="py-2 px-2 fw-bold text-primary">Khách hàng</th>
                            <th class="py-2 px-2 fw-bold text-primary d-none d-lg-table-cell">Liên hệ</th>
                            <th class="py-2 px-2 fw-bold text-primary text-center">Dịch vụ</th>
                            <th class="py-2 px-2 fw-bold text-primary d-none d-xl-table-cell">Ngày tạo</th>
                            <th class="py-2 px-2 fw-bold text-primary text-center">Thao tác</th>
                        </tr>
                    </thead>
                <tbody>
                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr style="border-bottom: 1px solid #e9ecef;">
                        <td class="py-2 px-2">
                            <span class="badge bg-primary px-1 py-1 small"><?php echo e($customer->customer_code); ?></span>
                        </td>
                        <td class="py-2 px-2">
                            <div class="d-flex align-items-center">
                                <div class="avatar-initial rounded-circle me-2"
                                    style="width: 28px; height: 28px; background: var(--primary-gradient); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">
                                    <?php echo e(strtoupper(substr($customer->name, 0, 1))); ?>

                                </div>
                                <div class="flex-fill" style="min-width: 0;">
                                    <div class="fw-bold text-truncate small" title="<?php echo e($customer->name); ?>"><?php echo e($customer->name); ?></div>
                                    <div class="d-lg-none text-muted small mt-1">
                                        <?php if($customer->email): ?>
                                        <div class="text-truncate">
                                            <i class="fas fa-envelope me-1 text-primary"></i><?php echo e(Str::limit($customer->email, 20)); ?>

                                        </div>
                                        <?php endif; ?>
                                        <?php if($customer->phone): ?>
                                        <div class="mt-1">
                                            <i class="fas fa-phone me-1 text-success"></i><?php echo e($customer->phone); ?>

                                        </div>
                                        <?php endif; ?>
                                        <div class="d-xl-none mt-1">
                                            <span class="badge bg-light text-dark small"><?php echo e($customer->created_at->format('d/m/y')); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="py-2 px-2 d-none d-lg-table-cell">
                            <div class="small">
                                <?php if($customer->email): ?>
                                <div class="mb-1 text-truncate" title="<?php echo e($customer->email); ?>">
                                    <a href="mailto:<?php echo e($customer->email); ?>" class="text-decoration-none text-primary">
                                        <i class="fas fa-envelope me-1"></i><?php echo e(Str::limit($customer->email, 25)); ?>

                                    </a>
                                </div>
                                <?php endif; ?>
                                <?php if($customer->phone): ?>
                                <div>
                                    <a href="tel:<?php echo e($customer->phone); ?>" class="text-decoration-none text-success">
                                        <i class="fas fa-phone me-1"></i><?php echo e($customer->phone); ?>

                                    </a>
                                </div>
                                <?php endif; ?>
                                <?php if(!$customer->email && !$customer->phone): ?>
                                <span class="text-muted small">— Chưa có</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="py-2 px-2 text-center">
                            <?php
                            $serviceCount = $customer->customerServices->count();
                            $activeServices = $customer->customerServices->where('status', 'active')->count();
                            $expiredServices = $customer->customerServices->where('status', 'expired')->count();
                            $loginEmails = $customer->customerServices->whereNotNull('login_email')->pluck('login_email')->unique();
                            ?>
                            <div class="d-flex flex-column align-items-center gap-1">
                                <?php if($serviceCount > 0): ?>
                                <div class="d-flex gap-1">
                                    <span class="badge bg-primary px-1 py-1 small">
                                        <?php echo e($serviceCount); ?>

                                    </span>
                                    <?php if($activeServices > 0): ?>
                                    <span class="badge bg-success px-1 py-1 small">
                                        <?php echo e($activeServices); ?>

                                    </span>
                                    <?php endif; ?>
                                    <?php if($expiredServices > 0): ?>
                                    <span class="badge bg-danger px-1 py-1 small">
                                        <?php echo e($expiredServices); ?>

                                    </span>
                                    <?php endif; ?>
                                </div>
                                <?php else: ?>
                                <span class="badge bg-secondary px-1 py-1 small">0</span>
                                <?php endif; ?>
                                <?php if($loginEmails->count() > 0): ?>
                                <div class="mt-1">
                                    <?php $__currentLoopData = $loginEmails->take(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $loginEmail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <small class="text-muted" title="<?php echo e($loginEmail); ?>">
                                        <i class="fas fa-envelope text-warning"></i>
                                    </small>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($loginEmails->count() > 1): ?>
                                    <small class="text-muted">+<?php echo e($loginEmails->count() - 1); ?></small>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="py-2 px-2 d-none d-xl-table-cell">
                            <div class="small text-center">
                                <div class="fw-semibold text-primary"><?php echo e($customer->created_at->format('d/m')); ?></div>
                                <div class="text-muted"><?php echo e($customer->created_at->format('H:i')); ?></div>
                            </div>
                        </td>
                        <td class="py-2 px-2 text-center">
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('admin.customers.show', $customer)); ?>"
                                    class="btn btn-outline-info btn-sm"
                                    title="Xem chi tiết" data-bs-toggle="tooltip">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('admin.customers.edit', $customer)); ?>"
                                    class="btn btn-outline-warning btn-sm"
                                    title="Chỉnh sửa" data-bs-toggle="tooltip">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo e(route('admin.customers.assign-service', $customer)); ?>"
                                    class="btn btn-outline-success btn-sm"
                                    title="Gán dịch vụ" data-bs-toggle="tooltip">
                                    <i class="fas fa-plus"></i>
                                </a>
                                <button type="button"
                                    class="btn btn-outline-danger btn-sm delete-btn"
                                    title="Xóa khách hàng"
                                    data-bs-toggle="tooltip"
                                    data-customer-name="<?php echo e($customer->name); ?>"
                                    data-delete-url="<?php echo e(route('admin.customers.destroy', $customer)); ?>">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 bg-light">
            <div class="card-body py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="fs-6 text-muted d-none d-md-block">
                        <i class="fas fa-list me-2 text-primary"></i>
                        Hiển thị <?php echo e($customers->firstItem() ?? 0); ?> - <?php echo e($customers->lastItem() ?? 0); ?> trong tổng số <?php echo e(number_format($customers->total())); ?> khách hàng
                    </div>
                    <div class="ms-auto">
                        <div class="pagination-wrapper" style="font-size: 1.1rem;">
                            <?php echo e($customers->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-users fa-5x text-muted opacity-50"></i>
                </div>
                <h4 class="text-muted mb-3">Không tìm thấy khách hàng nào</h4>
                <p class="text-muted fs-5 mb-4">
                    <?php if(request()->hasAny(['search', 'service_package_id', 'service_status', 'login_email', 'date_from', 'date_to'])): ?>
                    Không có khách hàng nào khớp với tiêu chí tìm kiếm của bạn.
                    <?php else: ?>
                    Hệ thống chưa có khách hàng nào. Hãy thêm khách hàng đầu tiên!
                    <?php endif; ?>
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <?php if(request()->hasAny(['search', 'service_package_id', 'service_status', 'login_email', 'date_from', 'date_to'])): ?>
                    <a href="<?php echo e(route('admin.customers.index')); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-refresh me-2"></i>Xem tất cả
                    </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('admin.customers.create')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Thêm khách hàng mới
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Add Customer Modal -->
    <div class="modal fade" id="quickAddModal" tabindex="-1" aria-labelledby="quickAddModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header bg-primary text-white py-4">
                    <h4 class="modal-title fw-bold" id="quickAddModalLabel">
                        <i class="fas fa-user-plus me-3"></i>
                        Thêm khách hàng nhanh
                    </h4>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <form id="quickAddForm" method="POST" action="<?php echo e(route('admin.customers.store')); ?>">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="return_page" value="<?php echo e(request('page', 1)); ?>">
                    <input type="hidden" name="return_search" value="<?php echo e(request('search')); ?>">

                    <div class="modal-body p-4">
                        <div class="mb-4">
                            <label for="quick_name" class="form-label fw-semibold fs-5">
                                <i class="fas fa-user me-2 text-primary"></i>
                                Tên khách hàng <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control form-control-lg" id="quick_name" name="name" required
                                placeholder="Nhập tên đầy đủ của khách hàng">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="quick_email" class="form-label fw-semibold fs-6">
                                    <i class="fas fa-envelope me-2 text-info"></i>Email
                                </label>
                                <input type="email" class="form-control form-control-lg" id="quick_email" name="email"
                                    placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="quick_phone" class="form-label fw-semibold fs-6">
                                    <i class="fas fa-phone me-2 text-success"></i>Số điện thoại
                                </label>
                                <input type="text" class="form-control form-control-lg" id="quick_phone" name="phone"
                                    placeholder="0xxx xxx xxx">
                            </div>
                        </div>
                        <div class="alert alert-info border-0 bg-light-info">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            <span class="fs-6">Mã khách hàng sẽ được tự động tạo theo định dạng KUN#####</span>
                        </div>
                    </div>
                    <div class="modal-footer border-0 p-4">
                        <button type="button" class="btn btn-outline-secondary btn-lg px-4" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            Hủy
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg px-4">
                            <i class="fas fa-save me-2"></i>
                            Lưu khách hàng
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header bg-danger text-white py-4">
                    <h4 class="modal-title fw-bold" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle me-3"></i>
                        Xác nhận xóa khách hàng
                    </h4>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-user-times fa-5x text-danger mb-4"></i>
                        <h5 class="mb-3">Bạn có chắc chắn muốn xóa khách hàng:</h5>
                        <p class="fs-4 fw-bold text-primary mb-4" id="customerNameToDelete"></p>
                    </div>
                    <div class="alert alert-warning border-0 bg-light-warning">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                        <span class="fs-6 fw-semibold">Cảnh báo: Hành động này không thể hoàn tác!</span>
                    </div>
                </div>
                <div class="modal-footer border-0 justify-content-center p-4">
                    <button type="button" class="btn btn-outline-secondary btn-lg px-4 me-3" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Hủy bỏ
                    </button>
                    <form id="deleteForm" method="POST" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger btn-lg px-4">
                            <i class="fas fa-trash me-2"></i>
                            Xóa khách hàng
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php $__env->stopSection(); ?>

    <?php $__env->startSection('styles'); ?>
    <style>
        /* Enhanced Customer Interface Styles */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        /* Make table more spacious */
        .table tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        /* Enhanced badges */
        .badge {
            font-weight: 600;
            letter-spacing: 0.5px;
            border-radius: 8px;
        }

        /* Button hover effects */
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        /* Avatar enhancements */
        .avatar-initial {
            border: 3px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Form control enhancements */
        .form-control-lg {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control-lg:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        /* Card enhancements */
        .card {
            border-radius: 15px;
            overflow: hidden;
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }

        /* Modal enhancements */
        .modal-content {
            border-radius: 20px;
            overflow: hidden;
        }

        .modal-header {
            border-radius: 20px 20px 0 0 !important;
        }

        /* Button group spacing */
        .btn-group .btn {
            margin: 0 2px;
            border-radius: 8px !important;
        }

        /* Pagination styling */
        .pagination-wrapper .page-link {
            font-size: 1.1rem;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 0 2px;
        }

        /* Light backgrounds */
        .bg-light-info {
            background-color: rgba(13, 202, 240, 0.1) !important;
        }

        .bg-light-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .btn-group .btn {
                font-size: 0.9rem;
                padding: 8px 12px;
            }

            .modal-dialog {
                margin: 1rem;
            }
        }
    </style>
    <?php $__env->stopSection(); ?>

    <?php $__env->startSection('scripts'); ?>
    <script>
        // Confirm delete function - global scope
        function confirmDelete(customerName, deleteUrl) {
            document.getElementById('customerNameToDelete').textContent = customerName;
            document.getElementById('deleteForm').action = deleteUrl;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Handle delete buttons
            document.querySelectorAll('.delete-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const customerName = this.getAttribute('data-customer-name');
                    const deleteUrl = this.getAttribute('data-delete-url');
                    confirmDelete(customerName, deleteUrl);
                });
            });

            // Quick add form validation
            const quickAddForm = document.getElementById('quickAddForm');
            if (quickAddForm) {
                quickAddForm.addEventListener('submit', function(e) {
                    const nameInput = document.getElementById('quick_name');
                    if (!nameInput.value.trim()) {
                        e.preventDefault();
                        nameInput.focus();
                        nameInput.classList.add('is-invalid');
                    }
                });
            }
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/customers/index.blade.php ENDPATH**/ ?>