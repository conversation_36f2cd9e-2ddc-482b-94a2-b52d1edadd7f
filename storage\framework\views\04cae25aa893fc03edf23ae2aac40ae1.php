

<?php $__env->startSection('title', 'Báo cáo thống kê hàng ngày'); ?>
<?php $__env->startSection('page-title', 'Báo cáo thống kê hàng ngày'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Báo cáo thống kê dịch vụ hàng ngày</h5>
                        <small class="text-muted">Xem tổng quan dịch vụ kích hoạt, hết hạn trong ngày</small>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('admin.customer-services.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Quay lại
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Date Filter -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <form method="GET" class="d-flex gap-2">
                            <input type="date" 
                                   name="date" 
                                   class="form-control" 
                                   value="<?php echo e(request('date', $selectedDate->format('Y-m-d'))); ?>">
                            <button type="submit" class="btn btn-primary">Xem</button>
                            <a href="<?php echo e(route('admin.customer-services.daily-report')); ?>" class="btn btn-outline-secondary">Hôm nay</a>
                        </form>
                    </div>
                    <div class="col-md-8 text-end">
                        <h4 class="text-primary"><?php echo e($selectedDate->format('d/m/Y')); ?> - <?php echo e($selectedDate->translatedFormat('l')); ?></h4>
                    </div>
                </div>

                <!-- Thống kê tổng quan -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h2 class="mb-1"><?php echo e($stats['activated']['total_services']); ?></h2>
                                <h6 class="mb-1">Dịch vụ kích hoạt</h6>
                                <small><?php echo e($stats['activated']['unique_customers']); ?> khách hàng</small>
                                <hr class="my-2">
                                <strong><?php echo e(number_format($stats['activated']['revenue_estimate'])); ?>₫</strong>
                                <br><small>Doanh thu ước tính</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h2 class="mb-1"><?php echo e($stats['expired']['total_services']); ?></h2>
                                <h6 class="mb-1">Dịch vụ hết hạn</h6>
                                <small><?php echo e($stats['expired']['unique_customers']); ?> khách hàng</small>
                                <hr class="my-2">
                                <small>Cần liên hệ gia hạn</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h2 class="mb-1"><?php echo e($stats['expiring_soon']['total_services']); ?></h2>
                                <h6 class="mb-1">Sắp hết hạn (5 ngày)</h6>
                                <small><?php echo e($stats['expiring_soon']['not_reminded']); ?> chưa nhắc</small>
                                <hr class="my-2">
                                <small><?php echo e($stats['expiring_soon']['reminded']); ?> đã nhắc</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chi tiết dịch vụ kích hoạt -->
                <?php if($activatedToday->count() > 0): ?>
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-play-circle me-2"></i>
                                        Dịch vụ kích hoạt hôm nay (<?php echo e($activatedToday->count()); ?>)
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-sm mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Khách hàng</th>
                                                    <th>Dịch vụ</th>
                                                    <th>Giá</th>
                                                    <th>Hết hạn</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $activatedToday; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo e($service->customer->name); ?></strong>
                                                            <br><small class="text-muted"><?php echo e($service->customer->customer_code); ?></small>
                                                        </td>
                                                        <td><?php echo e($service->servicePackage->name); ?></td>
                                                        <td><?php echo e(number_format($service->servicePackage->price ?? 0)); ?>₫</td>
                                                        <td><?php echo e($service->expires_at ? $service->expires_at->format('d/m/Y') : 'Không giới hạn'); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Top gói dịch vụ</h6>
                                </div>
                                <div class="card-body">
                                    <?php $__currentLoopData = $stats['activated']['by_package']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $packageName => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span><?php echo e($packageName); ?></span>
                                            <div class="text-end">
                                                <strong><?php echo e($data['count']); ?></strong>
                                                <br><small class="text-muted"><?php echo e(number_format($data['revenue'])); ?>₫</small>
                                            </div>
                                        </div>
                                        <?php if(!$loop->last): ?><hr class="my-1"><?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Chi tiết dịch vụ hết hạn -->
                <?php if($expiredToday->count() > 0): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-stop-circle me-2"></i>
                                        Dịch vụ hết hạn hôm nay (<?php echo e($expiredToday->count()); ?>)
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-sm mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Khách hàng</th>
                                                    <th>Dịch vụ</th>
                                                    <th>Email liên hệ</th>
                                                    <th>Ngày kích hoạt</th>
                                                    <th>Thao tác</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $expiredToday; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo e($service->customer->name); ?></strong>
                                                            <br><small class="text-muted"><?php echo e($service->customer->customer_code); ?></small>
                                                        </td>
                                                        <td><?php echo e($service->servicePackage->name); ?></td>
                                                        <td><?php echo e($service->customer->email ?: 'Chưa có'); ?></td>
                                                        <td><?php echo e($service->activated_at ? $service->activated_at->format('d/m/Y') : 'N/A'); ?></td>
                                                        <td>
                                                            <a href="<?php echo e(route('admin.customer-services.edit', $service)); ?>" 
                                                               class="btn btn-sm btn-warning"
                                                               title="Gia hạn">
                                                                <i class="fas fa-clock"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Dịch vụ sắp hết hạn trong 5 ngày -->
                <?php if($expiringSoon->count() > 0): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-warning">
                                    <h6 class="mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Dịch vụ sắp hết hạn trong 5 ngày (<?php echo e($expiringSoon->count()); ?>)
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-sm mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Khách hàng</th>
                                                    <th>Dịch vụ</th>
                                                    <th>Hết hạn</th>
                                                    <th>Còn lại</th>
                                                    <th>Nhắc nhở</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $expiringSoon; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr class="<?php echo e($service->getDaysRemaining() <= 1 ? 'table-danger' : ($service->getDaysRemaining() <= 2 ? 'table-warning' : '')); ?>">
                                                        <td>
                                                            <strong><?php echo e($service->customer->name); ?></strong>
                                                            <br><small class="text-muted"><?php echo e($service->customer->customer_code); ?></small>
                                                        </td>
                                                        <td><?php echo e($service->servicePackage->name); ?></td>
                                                        <td><?php echo e($service->expires_at->format('d/m/Y')); ?></td>
                                                        <td>
                                                            <strong class="<?php echo e($service->getDaysRemaining() <= 1 ? 'text-danger' : 'text-warning'); ?>">
                                                                <?php echo e($service->getDaysRemaining()); ?> ngày
                                                            </strong>
                                                        </td>
                                                        <td>
                                                            <?php if($service->reminder_sent): ?>
                                                                <span class="badge bg-success">Đã nhắc</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Chưa nhắc</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Trường hợp không có dữ liệu -->
                <?php if($activatedToday->count() === 0 && $expiredToday->count() === 0 && $expiringSoon->count() === 0): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Không có hoạt động nào trong ngày <?php echo e($selectedDate->format('d/m/Y')); ?></h5>
                        <p class="text-muted">Không có dịch vụ kích hoạt, hết hạn hoặc sắp hết hạn.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/customer-services/daily-report.blade.php ENDPATH**/ ?>